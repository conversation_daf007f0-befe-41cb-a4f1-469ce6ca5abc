/**
 * Responsive Admin Panel JavaScript
 * Handles sidebar toggle, responsive behavior, and navigation
 */

class ResponsiveAdminPanel {
  constructor() {
    this.sidebar = document.querySelector('.admin-sidebar');
    this.content = document.querySelector('.admin-content');
    this.overlay = document.querySelector('.sidebar-overlay');
    this.toggleBtn = document.querySelector('.topbar-toggle');
    this.closeBtn = document.querySelector('.sidebar-close');
    
    this.isMobile = window.innerWidth <= 767.98;
    this.isTablet = window.innerWidth >= 768 && window.innerWidth <= 991.98;
    this.isDesktop = window.innerWidth >= 992;
    this.sidebarCollapsed = false;
    
    this.init();
  }
  
  init() {
    this.createOverlay();
    this.setupEventListeners();
    this.handleInitialState();
    this.setupResizeHandler();
    this.setupKeyboardNavigation();
    this.setActiveNavItem();
    this.handleVisibilityChange();
    this.addSmoothScrolling();
  }
  
  createOverlay() {
    if (!this.overlay) {
      this.overlay = document.createElement('div');
      this.overlay.className = 'sidebar-overlay';
      document.body.appendChild(this.overlay);
    }
    
    this.overlay.addEventListener('click', () => {
      this.hideSidebar();
    });
  }
  
  setupEventListeners() {
    // Toggle button
    if (this.toggleBtn) {
      this.toggleBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleSidebar();
      });
    }
    
    // Close button
    if (this.closeBtn) {
      this.closeBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.hideSidebar();
      });
    }
    
    // Navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        // Don't prevent default for actual navigation
        if (this.isMobile || this.isTablet) {
          // Close sidebar after navigation on mobile/tablet
          setTimeout(() => {
            this.hideSidebar();
          }, 150);
        }
      });
    });
  }
  
  handleInitialState() {
    this.updateBreakpoints();
    
    if (this.isDesktop) {
      this.showSidebar();
    } else {
      this.hideSidebar();
    }
  }
  
  setupResizeHandler() {
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        this.updateBreakpoints();
        this.handleResponsiveChanges();
      }, 150);
    });
  }
  
  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // ESC key to close sidebar on mobile/tablet
      if (e.key === 'Escape' && (this.isMobile || this.isTablet)) {
        this.hideSidebar();
      }
      
      // Alt + S to toggle sidebar
      if (e.altKey && e.key === 's') {
        e.preventDefault();
        this.toggleSidebar();
      }
    });
  }
  
  updateBreakpoints() {
    const width = window.innerWidth;
    this.isMobile = width <= 767.98;
    this.isTablet = width >= 768 && width <= 991.98;
    this.isDesktop = width >= 992;
  }
  
  handleResponsiveChanges() {
    if (this.isDesktop) {
      this.showSidebar();
      this.hideOverlay();
    } else {
      this.hideSidebar();
    }
  }
  
  showSidebar() {
    if (this.sidebar) {
      this.sidebar.classList.add('show');
      
      if (this.isMobile || this.isTablet) {
        this.showOverlay();
      }
      
      // Add animation class
      this.sidebar.classList.add('slide-in-left');
      setTimeout(() => {
        this.sidebar.classList.remove('slide-in-left');
      }, 300);
    }
  }
  
  hideSidebar() {
    if (this.sidebar) {
      this.sidebar.classList.remove('show');
      this.hideOverlay();
      
      // Add animation class
      this.sidebar.classList.add('slide-out-left');
      setTimeout(() => {
        this.sidebar.classList.remove('slide-out-left');
      }, 300);
    }
  }
  
  toggleSidebar() {
    if (this.isDesktop && this.sidebar.classList.contains('show')) {
      // On desktop, toggle collapsed state instead of hiding
      this.toggleCollapsed();
    } else {
      // On mobile/tablet or when sidebar is hidden, toggle visibility
      if (this.sidebar.classList.contains('show')) {
        this.hideSidebar();
      } else {
        this.showSidebar();
      }
    }
  }
  
  toggleCollapsed() {
    if (this.isDesktop) {
      this.sidebarCollapsed = !this.sidebarCollapsed;
      
      if (this.sidebarCollapsed) {
        this.sidebar.classList.add('collapsed');
        this.content.classList.add('sidebar-collapsed');
      } else {
        this.sidebar.classList.remove('collapsed');
        this.content.classList.remove('sidebar-collapsed');
      }
    }
  }
  
  showOverlay() {
    if (this.overlay) {
      this.overlay.classList.add('show');
    }
  }
  
  hideOverlay() {
    if (this.overlay) {
      this.overlay.classList.remove('show');
    }
  }
  
  setActiveNavItem() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.php';
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
      link.classList.remove('active');
      
      const href = link.getAttribute('href');
      if (href) {
        const linkPage = href.split('/').pop();
        if (linkPage === currentPage || 
            (currentPage === 'index.php' && (linkPage === 'index.php' || linkPage === ''))) {
          link.classList.add('active');
        }
      }
    });
  }
  
  // Public methods
  collapse() {
    if (this.isDesktop) {
      this.sidebarCollapsed = true;
      this.sidebar.classList.add('collapsed');
      this.content.classList.add('sidebar-collapsed');
    }
  }
  
  expand() {
    if (this.isDesktop) {
      this.sidebarCollapsed = false;
      this.sidebar.classList.remove('collapsed');
      this.content.classList.remove('sidebar-collapsed');
    }
  }
  
  isVisible() {
    return this.sidebar && this.sidebar.classList.contains('show');
  }
  
  isCollapsed() {
    return this.sidebarCollapsed;
  }

  // Save user preferences
  savePreferences() {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('adminPanelCollapsed', this.sidebarCollapsed);
    }
  }

  // Load user preferences
  loadPreferences() {
    if (typeof localStorage !== 'undefined') {
      const collapsed = localStorage.getItem('adminPanelCollapsed');
      if (collapsed === 'true' && this.isDesktop) {
        this.collapse();
      }
    }
  }

  // Handle page visibility changes
  handleVisibilityChange() {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // Page is hidden, save preferences
        this.savePreferences();
      }
    });
  }

  // Add smooth scrolling for navigation
  addSmoothScrolling() {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        // Add loading state
        link.style.opacity = '0.7';
        setTimeout(() => {
          link.style.opacity = '1';
        }, 200);
      });
    });
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.adminPanel = new ResponsiveAdminPanel();

  // Add additional initialization
  if (window.adminPanel) {
    window.adminPanel.loadPreferences();
    window.adminPanel.handleVisibilityChange();
    window.adminPanel.addSmoothScrolling();
  }
});

// Handle page unload
window.addEventListener('beforeunload', () => {
  if (window.adminPanel) {
    window.adminPanel.savePreferences();
  }
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ResponsiveAdminPanel;
}
