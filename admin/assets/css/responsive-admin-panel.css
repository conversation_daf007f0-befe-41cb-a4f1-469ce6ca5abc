/* Responsive Admin Side Panel Styles */

:root {
  /* Responsive Breakpoints */
  --mobile-max: 767.98px;
  --tablet-min: 768px;
  --tablet-max: 991.98px;
  --desktop-min: 992px;
  
  /* Sidebar Variables */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 60px;
  
  /* Colors */
  --sidebar-bg: #1a1a1a;
  --sidebar-text: #ffffff;
  --sidebar-text-muted: #b0b0b0;
  --sidebar-hover-bg: #2a2a2a;
  --sidebar-active-bg: #333333;
  --sidebar-border: #333333;
  
  /* Animation Variables */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.4s ease;
}

/* Main Layout Structure */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* Sidebar Wrapper */
.admin-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: var(--sidebar-width);
  height: 100vh;
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  transform: translateX(-100%);
  transition: transform var(--transition-normal);
  z-index: 1050;
  overflow-y: auto;
  overflow-x: hidden;
}

.admin-sidebar.show {
  transform: translateX(0);
}

/* Sidebar Header */
.sidebar-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid var(--sidebar-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-brand {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--sidebar-text);
  text-decoration: none;
}

.sidebar-close {
  background: none;
  border: none;
  color: var(--sidebar-text);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color var(--transition-fast);
}

.sidebar-close:hover {
  background-color: var(--sidebar-hover-bg);
}

/* Navigation Menu */
.sidebar-nav {
  padding: 1rem 0;
}

.nav-section {
  margin-bottom: 1.5rem;
}

.nav-section-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--sidebar-text-muted);
  padding: 0.5rem 1rem;
  margin-bottom: 0.5rem;
  letter-spacing: 0.5px;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--sidebar-text);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-radius: 0;
  position: relative;
}

.nav-link:hover {
  background-color: var(--sidebar-hover-bg);
  color: var(--sidebar-text);
}

.nav-link.active {
  background-color: var(--sidebar-active-bg);
  color: var(--sidebar-text);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #007bff;
}

.nav-icon {
  width: 1.25rem;
  margin-right: 0.75rem;
  text-align: center;
  font-size: 1rem;
}

.nav-text {
  flex: 1;
  font-weight: 500;
}

/* Main Content Area */
.admin-content {
  flex: 1;
  margin-left: 0;
  transition: margin-left var(--transition-normal);
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* Top Navigation Bar */
.admin-topbar {
  background-color: #ffffff;
  border-bottom: 1px solid #e9ecef;
  padding: 0.75rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 1040;
}

.topbar-toggle {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color var(--transition-fast);
}

.topbar-toggle:hover {
  background-color: #f8f9fa;
}

.topbar-user {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

/* Overlay for mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Responsive Design */

/* Desktop (992px and up) */
@media (min-width: 992px) {
  .admin-sidebar {
    position: relative;
    transform: translateX(0);
    z-index: auto;
  }
  
  .admin-content {
    margin-left: var(--sidebar-width);
  }
  
  .sidebar-overlay {
    display: none;
  }
  
  .sidebar-close {
    display: none;
  }
  
  /* Collapsed state for desktop */
  .admin-sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
  }
  
  .admin-sidebar.collapsed .nav-text,
  .admin-sidebar.collapsed .nav-section-title,
  .admin-sidebar.collapsed .sidebar-brand {
    opacity: 0;
    visibility: hidden;
  }
  
  .admin-sidebar.collapsed .nav-link {
    justify-content: center;
    padding-left: 0;
    padding-right: 0;
  }
  
  .admin-sidebar.collapsed .nav-icon {
    margin-right: 0;
  }
  
  .admin-content.sidebar-collapsed {
    margin-left: var(--sidebar-collapsed-width);
  }
}

/* Tablet (768px to 991px) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .admin-sidebar {
    width: var(--sidebar-width);
  }
  
  .topbar-toggle {
    display: block;
  }
}

/* Mobile (up to 767px) */
@media (max-width: 767.98px) {
  .admin-sidebar {
    width: 100%;
    max-width: 320px;
  }
  
  .topbar-toggle {
    display: block;
  }
  
  .admin-content {
    margin-left: 0;
  }
}

/* Animation Classes */
.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.slide-out-left {
  animation: slideOutLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

/* Accessibility */
.nav-link:focus {
  outline: 2px solid #007bff;
  outline-offset: -2px;
}

/* Integration with existing dashboard styles */
.admin-content .container-fluid {
  padding-top: 0;
}

.admin-content .card {
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.admin-content .btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.admin-content .btn-success {
  background-color: #28a745;
  border-color: #28a745;
}

/* Ensure compatibility with existing dashboard cards */
.dashboard-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

/* Fix for flash messages positioning */
.alert {
  margin-bottom: 1rem;
  border-radius: 0.5rem;
}

/* Responsive table improvements */
@media (max-width: 767.98px) {
  .table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .admin-content .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Print Styles */
@media print {
  .admin-sidebar,
  .admin-topbar {
    display: none;
  }

  .admin-content {
    margin-left: 0;
  }
}
