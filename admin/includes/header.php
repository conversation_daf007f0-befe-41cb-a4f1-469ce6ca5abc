<?php
// Start output buffering
ob_start();

require_once 'config.php';
require_once 'database.php';
require_once 'auth.php';
require_once 'utilities.php';
require_once 'permissions.php';

// Initialize auth
$auth = new Auth();

// CSRF token generation
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Check if user is logged in
if (!$auth->isLoggedIn() && basename($_SERVER['PHP_SELF']) !== 'login.php') {
    Utilities::redirect('login.php');
}

// Check session timeout
if (!$auth->checkSessionTimeout() && basename($_SERVER['PHP_SELF']) !== 'login.php') {
    Utilities::setFlashMessage('warning', 'Your session has expired. Please log in again.');
    Utilities::redirect('login.php');
}

// Skip permission check for login, logout, unauthorized pages
$noCheckPages = ['login.php', 'logout.php', 'unauthorized.php', 'index.php'];
$currentPage = basename($_SERVER['PHP_SELF']);

// Check if the current page requires permission
if (!in_array($currentPage, $noCheckPages)) {
    // Get the required permission for the current page
    $requiredPermission = getRequiredPermissionForCurrentPage();

    // If the page requires permission, check if the user has it
    if ($requiredPermission !== null) {
        // For staff members, check specific permissions
        if ($_SESSION['role'] === 'staff') {
            if (!hasPermission($requiredPermission)) {
                redirectToUnauthorized();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Google Fonts - Inter -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">

    <!-- Modern CSS -->
    <link rel="stylesheet" href="assets/css/modern.css">

    <!-- Admin Table CSS -->
    <link rel="stylesheet" href="assets/css/admin-table.css">

    <!-- Global Dropdown Fix CSS -->
    <link rel="stylesheet" href="assets/css/global-dropdown-fix.css">

    <!-- Responsive Admin Panel CSS -->
    <link rel="stylesheet" href="assets/css/responsive-admin-panel.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/img/favicon.svg">
    <link rel="alternate icon" href="assets/img/favicon.ico" type="image/x-icon">

    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
<?php Utilities::displayFlashMessages(); ?>

<div class="admin-layout">
    <!-- Responsive Admin Sidebar -->
    <nav class="admin-sidebar" id="adminSidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <a href="index.php" class="sidebar-brand">
                <i class="fas fa-dumbbell me-2"></i>
                KFT Admin
            </a>
            <button class="sidebar-close" id="sidebarClose">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Navigation Menu -->
        <div class="sidebar-nav">
            <!-- Main Section -->
            <div class="nav-section">
                <div class="nav-section-title">Main</div>
                <div class="nav-item">
                    <a href="index.php" class="nav-link">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Overview Dashboard</span>
                    </a>
                </div>
            </div>

            <!-- Management Section -->
            <div class="nav-section">
                <div class="nav-section-title">Management</div>
                <div class="nav-item">
                    <a href="staff_management.php" class="nav-link">
                        <i class="fas fa-users-cog nav-icon"></i>
                        <span class="nav-text">Staff Management</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="settings.php" class="nav-link">
                        <i class="fas fa-cogs nav-icon"></i>
                        <span class="nav-text">Platform Settings</span>
                    </a>
                </div>
            </div>

            <!-- Courses Section -->
            <div class="nav-section">
                <div class="nav-section-title">Courses</div>
                <div class="nav-item">
                    <a href="courses.php" class="nav-link">
                        <i class="fas fa-graduation-cap nav-icon"></i>
                        <span class="nav-text">All Courses</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="course_assign.php" class="nav-link">
                        <i class="fas fa-user-plus nav-icon"></i>
                        <span class="nav-text">Assign Courses</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="users.php" class="nav-link">
                        <i class="fas fa-users nav-icon"></i>
                        <span class="nav-text">User Management</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="course_add.php" class="nav-link">
                        <i class="fas fa-plus-circle nav-icon"></i>
                        <span class="nav-text">Create Course</span>
                    </a>
                </div>
            </div>

            <!-- Account Section -->
            <div class="nav-section">
                <div class="nav-section-title">Account</div>
                <div class="nav-item">
                    <a href="logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">Sign Out</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content Area -->
    <div class="admin-content" id="adminContent">
        <!-- Top Navigation Bar -->
        <div class="admin-topbar">
            <button class="topbar-toggle" id="topbarToggle">
                <i class="fas fa-bars"></i>
            </button>

            <div class="topbar-user">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($_SESSION['name'] ?? $_SESSION['username'] ?? 'U', 0, 1)); ?>
                </div>
                <span class="d-none d-md-inline">
                    <?php echo htmlspecialchars($_SESSION['name'] ?? $_SESSION['username'] ?? 'User'); ?>
                </span>
            </div>
        </div>

        <!-- Page Content Container -->
        <div class="container-fluid px-4 py-3">
